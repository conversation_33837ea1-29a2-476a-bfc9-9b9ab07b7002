# 支付宝配置参数获取指南

本指南详细说明如何获取支付宝支付所需的各项配置参数。

## 🔧 配置参数说明

### 1. 支付宝网关地址 (alipay.gateway)

**用途**: 支付宝API的请求地址

**沙箱环境**: `https://openapi-sandbox.dl.alipaydev.com/gateway.do`
**正式环境**: `https://openapi.alipay.com/gateway.do`

**配置建议**: 开发和测试阶段使用沙箱环境，上线时切换到正式环境

---

### 2. 应用ID (alipay.app-id)

**用途**: 标识您的应用

**获取步骤**:
1. 访问 [支付宝开放平台](https://open.alipay.com/)
2. 登录您的支付宝账号
3. 进入"控制台" -> "应用管理"
4. 选择您的应用 -> 在应用详情页面可以看到APPID

**示例格式**: `2021000000000000` (16位数字)

**注意事项**: 
- 沙箱环境和正式环境的APPID是不同的
- 确保应用状态为"已上线"或"开发中"

---

### 3. 商户私钥 (alipay.merchant-private-key)

**用途**: 对请求参数进行签名，证明请求来源的合法性

**获取步骤**:
1. 下载支付宝官方密钥生成工具:
   - Windows: [支付宝开放平台助手](https://opendocs.alipay.com/common/02kipk)
   - Mac/Linux: 使用OpenSSL命令

2. 使用工具生成RSA2密钥对:
   ```bash
   # 生成私钥 (2048位)
   openssl genrsa -out app_private_key.pem 2048
   
   # 从私钥生成公钥
   openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem
   ```

3. 配置格式: 去掉头尾标识符的纯字符串
   ```
   原始私钥:
   -----BEGIN PRIVATE KEY-----
   MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7...
   -----END PRIVATE KEY-----
   
   配置中使用:
   MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7...
   ```

**安全提醒**: 
- 🔒 私钥绝对不能泄露给任何人
- 🔒 不要将私钥提交到Git等版本控制系统
- 🔒 生产环境建议使用环境变量或密钥管理服务

---

### 4. 支付宝公钥 (alipay.alipay-public-key)

**用途**: 验证支付宝返回数据的签名真实性

**获取步骤**:
1. 首先需要上传您的应用公钥到支付宝开放平台:
   - 控制台 -> 应用详情 -> 开发设置 -> 接口加签方式(密钥/证书)
   - 选择"公钥"模式
   - 上传您在第3步生成的应用公钥内容

2. 上传成功后，可以查看到支付宝公钥
3. 复制支付宝公钥内容(去掉头尾标识符)

**配置格式**: 同样去掉 `-----BEGIN PUBLIC KEY-----` 和 `-----END PUBLIC KEY-----`

---

### 5. 签名类型 (alipay.sign-type)

**推荐配置**: `RSA2`

**可选值**:
- `RSA2`: 使用SHA256WithRSA算法，安全性更高 ✅ 推荐
- `RSA`: 使用SHA1WithRSA算法，安全性较低 ❌ 不推荐

---

### 6. 回调地址配置

#### 同步回调地址 (alipay.return-url)
**用途**: 支付完成后用户浏览器跳转的页面

**配置要求**:
- 必须是http://或https://开头的完整URL
- 支付宝会在URL后面附加支付结果参数
- 开发环境可以使用localhost

**示例**: `http://localhost:8080/api/payments/alipay/return`

#### 异步通知地址 (alipay.notify-url)
**用途**: 支付宝服务器向您的服务器发送支付结果通知

**配置要求**:
- 必须是外网可访问的HTTP/HTTPS地址
- 开发环境需要内网穿透工具 (如ngrok、frp等)
- 服务器必须返回"success"字符串确认收到通知

**本地开发解决方案**:
```bash
# 使用ngrok进行内网穿透
ngrok http 8080
# 然后将生成的公网地址配置为notify-url
# 例如: https://xxxxx.ngrok.io/api/payments/alipay/notify
```

---

## 🚀 快速配置步骤

### 沙箱环境配置 (推荐新手)

1. **注册支付宝开放平台账号**
   - 访问 https://open.alipay.com/
   - 使用支付宝扫码登录

2. **进入沙箱环境**
   - 控制台 -> 沙箱 -> 沙箱应用
   - 查看沙箱应用的APPID

3. **配置密钥**
   - 在沙箱应用中上传您的应用公钥
   - 获取支付宝公钥

4. **修改配置文件**
   ```properties
   alipay.gateway=https://openapi-sandbox.dl.alipaydev.com/gateway.do
   alipay.app-id=你的沙箱APPID
   alipay.merchant-private-key=你生成的应用私钥
   alipay.alipay-public-key=支付宝沙箱公钥
   ```

### 正式环境配置

1. **创建正式应用**
   - 控制台 -> 应用管理 -> 创建应用
   - 选择"网页&移动应用"
   - 填写应用信息并提交审核

2. **应用上线**
   - 等待审核通过
   - 签署在线协议
   - 应用状态变为"已上线"

3. **配置正式环境参数**
   ```properties
   alipay.gateway=https://openapi.alipay.com/gateway.do
   alipay.app-id=你的正式APPID
   alipay.merchant-private-key=你的应用私钥
   alipay.alipay-public-key=支付宝正式公钥
   ```

---

## 🔍 配置验证

配置完成后，可以通过以下方式验证配置是否正确:

1. **启动应用**: `mvn spring-boot:run`
2. **发送测试请求**:
   ```bash
   curl -X POST http://localhost:8080/api/payments/alipay \
     -H "Content-Type: application/json" \
     -d '{
       "subject": "测试商品",
       "outTradeNo": "TEST001",
       "totalAmount": 0.01,
       "description": "测试支付"
     }'
   ```

3. **检查返回结果**: 应该返回包含HTML表单的JSON响应

---

## ⚠️ 常见问题

### Q1: 签名错误 (invalid-signature)
**解决方案**:
- 检查应用私钥格式是否正确
- 确认支付宝公钥是否正确获取
- 验证APPID是否正确

### Q2: 应用未授权 (forbidden)
**解决方案**:
- 确认应用状态为"已上线"或"开发中"
- 检查APPID是否与当前环境匹配

### Q3: 异步通知收不到
**解决方案**:
- 确保notify-url是外网可访问的
- 检查服务器防火墙设置
- 确保接口返回"success"字符串

### Q4: 沙箱环境支付失败
**解决方案**:
- 使用沙箱提供的买家账号进行支付
- 沙箱买家账号: <EMAIL> 密码: 111111

---

## 📞 技术支持

- [支付宝开放平台文档](https://opendocs.alipay.com/)
- [支付宝开发者社区](https://forum.alipay.com/)
- [官方技术支持](https://opensupport.alipay.com/support/home.htm)