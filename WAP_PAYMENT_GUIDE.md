# 手机网络支付（WAP）使用指南

## 概述

手机网络支付（WAP支付）是支付宝专门为移动端网页和APP内嵌H5场景提供的支付解决方案。适用于：

- 手机浏览器支付
- APP内嵌H5支付  
- 微信浏览器支付
- 扫码支付（生成支付链接二维码）

## API接口

### 1. 快速支付接口

**接口地址：** `GET /api/wap-pay/quick`

**参数：**
- `amount` - 支付金额（必填）
- `subject` - 商品标题（必填）

**示例：**
```bash
GET /api/wap-pay/quick?amount=0.01&subject=测试商品
```

**响应：** 直接返回支付表单HTML，浏览器会自动跳转到支付页面

---

### 2. 创建支付订单接口

**接口地址：** `POST /api/wap-pay/create`

**请求头：** `Content-Type: application/x-www-form-urlencoded`

**参数：**
- `outTradeNo` - 商户订单号（必填）
- `totalAmount` - 支付金额（必填）
- `subject` - 商品标题（必填）
- `description` - 商品描述（可选）

**示例：**
```bash
curl -X POST http://localhost:8080/api/wap-pay/create \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "outTradeNo=ORDER_001&totalAmount=0.01&subject=测试商品&description=这是测试"
```

**响应：**
```json
{
  "success": true,
  "outTradeNo": "ORDER_001",
  "paymentForm": "<form>...</form>",
  "message": "Payment form generated successfully"
}
```

---

### 3. 获取支付URL接口

**接口地址：** `GET /api/wap-pay/url`

**参数：**
- `amount` - 支付金额（必填）
- `subject` - 商品标题（必填）

**示例：**
```bash
GET /api/wap-pay/url?amount=0.01&subject=扫码支付测试
```

**响应：**
```json
{
  "success": true,
  "outTradeNo": "WAP_URL_1703567890123",
  "paymentUrl": "https://openapi.alipaydev.com/gateway.do?...",
  "qrCodeUrl": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=...",
  "message": "Payment URL generated successfully"
}
```

---

## 使用方式

### 方式一：直接跳转支付（推荐移动端）

```javascript
// 直接打开支付页面
window.location.href = '/api/wap-pay/quick?amount=0.01&subject=测试商品';

// 或在新窗口打开
window.open('/api/wap-pay/quick?amount=0.01&subject=测试商品', '_blank');
```

### 方式二：获取支付表单后处理

```javascript
// 1. 创建支付订单
fetch('/api/wap-pay/create', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'outTradeNo=ORDER_001&totalAmount=0.01&subject=测试商品'
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        // 2. 将支付表单插入页面并自动提交
        document.body.innerHTML = data.paymentForm;
        document.forms[0].submit();
    }
});
```

### 方式三：生成二维码扫码支付

```javascript
// 1. 获取支付URL
fetch('/api/wap-pay/url?amount=0.01&subject=扫码支付')
.then(response => response.json())
.then(data => {
    if (data.success) {
        // 2. 显示二维码
        document.getElementById('qrcode').innerHTML = 
            `<img src="${data.qrCodeUrl}" alt="支付二维码">`;
    }
});
```

---

## 回调处理

### 同步回调（用户支付完成后跳转）

**接口：** `GET /api/wap-pay/return`

用户支付完成后，支付宝会跳转到此地址，携带支付结果参数。

### 异步回调（服务器通知）

**接口：** `POST /api/wap-pay/notify`

支付宝服务器会向此地址发送支付结果通知，用于更新订单状态。

---

## 测试页面

访问测试页面进行功能测试：
```
http://localhost:8080/wap-payment-test.html
```

测试页面提供了：
- 快速支付测试
- 自定义参数支付
- 二维码生成测试
- API接口测试

---

## 产品码说明

WAP支付使用的产品码为 `QUICK_WAP_WAY`，这是支付宝官方指定的移动网页支付产品码。

---

## 注意事项

1. **金额格式**：支持最多两位小数，最小金额0.01元
2. **订单号**：必须唯一，建议包含时间戳
3. **字符编码**：统一使用UTF-8编码
4. **HTTPS**：生产环境必须使用HTTPS
5. **回调验证**：生产环境需要验证支付宝回调签名

---

## 错误处理

常见错误及解决方案：

| 错误代码 | 错误信息 | 解决方案 |
|---------|---------|---------|
| invalid-signature | 签名验证失败 | 检查密钥配置和签名方法 |
| invalid-timestamp | 时间戳无效 | 检查服务器时间是否准确 |
| TRADE_HAS_SUCCESS | 交易已成功 | 订单号重复，使用新的订单号 |
| invalid-format | 参数格式错误 | 检查参数格式和编码 |

---

## 配置要求

确保 `application.properties` 中的配置正确：

```properties
# 支付宝配置
alipay.app-id=你的应用ID
alipay.merchant-private-key=你的应用私钥
alipay.alipay-public-key=支付宝公钥
alipay.gateway-url=https://openapi.alipaydev.com/gateway.do
alipay.format=json
alipay.charset=UTF-8
alipay.sign-type=RSA2
alipay.return-url=http://localhost:8080/api/wap-pay/return
alipay.notify-url=http://localhost:8080/api/wap-pay/notify
```