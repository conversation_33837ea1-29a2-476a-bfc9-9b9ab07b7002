<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机网络支付测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .quick-btn {
            background-color: #52c41a;
            margin-right: 10px;
            width: auto;
            display: inline-block;
            padding: 8px 16px;
            margin-bottom: 10px;
        }
        .quick-btn:hover {
            background-color: #73d13d;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code img {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        .api-example {
            background-color: #fafafa;
            border: 1px solid #e8e8e8;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🔻 手机网络支付（WAP）接口测试</h1>
    
    <!-- API接口说明 -->
    <div class="card">
        <h2>📋 API接口说明</h2>
        <p><strong>手机网络支付</strong>（WAP支付）是支付宝为移动端网页和APP内嵌H5提供的支付解决方案。</p>
        
        <h3>1. 快速支付接口（GET）</h3>
        <div class="api-example">GET /api/wap-pay/quick?amount=0.01&subject=测试商品</div>
        <p>直接返回支付表单HTML，用户在浏览器中会自动跳转到支付页面。</p>
        
        <h3>2. 创建支付订单接口（POST）</h3>
        <div class="api-example">POST /api/wap-pay/create
Content-Type: application/x-www-form-urlencoded

outTradeNo=ORDER_001&totalAmount=0.01&subject=测试商品</div>
        <p>返回JSON格式的响应，包含支付表单HTML。</p>
        
        <h3>3. 获取支付URL接口（GET）</h3>
        <div class="api-example">GET /api/wap-pay/url?amount=0.01&subject=测试商品</div>
        <p>返回支付URL和二维码链接，适用于生成二维码扫码支付。</p>
    </div>

    <!-- 快速测试按钮 -->
    <div class="card">
        <h2>🚀 快速测试</h2>
        <p>点击下面的按钮快速测试不同的支付场景：</p>
        
        <button class="quick-btn" onclick="quickPay()">💰 快速支付 (0.01元)</button>
        <button class="quick-btn" onclick="generateQRCode()">📱 生成二维码</button>
        <button class="quick-btn" onclick="testAPI()">🔧 测试API</button>
        
        <div id="quickResult" class="result" style="display:none;"></div>
    </div>

    <!-- 自定义支付测试 -->
    <div class="card">
        <h2>🛠 自定义支付测试</h2>
        <form id="paymentForm">
            <div class="form-group">
                <label for="outTradeNo">商户订单号：</label>
                <input type="text" id="outTradeNo" name="outTradeNo" value="">
                <small>留空则自动生成</small>
            </div>
            
            <div class="form-group">
                <label for="totalAmount">支付金额：</label>
                <input type="number" id="totalAmount" name="totalAmount" value="0.01" step="0.01" min="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="subject">商品标题：</label>
                <input type="text" id="subject" name="subject" value="测试商品" required>
            </div>
            
            <div class="form-group">
                <label for="description">商品描述：</label>
                <textarea id="description" name="description" rows="3" placeholder="可选，商品的详细描述"></textarea>
            </div>
            
            <button type="submit">创建支付订单</button>
        </form>
        
        <div id="paymentResult" class="result" style="display:none;"></div>
    </div>

    <!-- 二维码生成 -->
    <div class="card">
        <h2>📲 二维码支付</h2>
        <p>生成二维码供手机扫码支付：</p>
        
        <form id="qrForm">
            <div class="form-group">
                <label for="qrAmount">支付金额：</label>
                <input type="number" id="qrAmount" name="amount" value="0.01" step="0.01" min="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="qrSubject">商品标题：</label>
                <input type="text" id="qrSubject" name="subject" value="扫码测试商品" required>
            </div>
            
            <button type="submit">生成支付二维码</button>
        </form>
        
        <div id="qrResult" class="result" style="display:none;"></div>
        <div id="qrCodeDisplay" class="qr-code" style="display:none;"></div>
    </div>

    <script>
        // 自动生成订单号
        function generateOrderNo() {
            return 'WAP_TEST_' + Date.now();
        }
        
        // 设置默认订单号
        document.getElementById('outTradeNo').value = generateOrderNo();

        // 快速支付测试
        function quickPay() {
            const url = `/api/wap-pay/quick?amount=0.01&subject=${encodeURIComponent('快速测试支付')}`;
            window.open(url, '_blank');
        }

        // 生成二维码测试
        function generateQRCode() {
            const resultDiv = document.getElementById('quickResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在生成二维码...';

            fetch(`/api/wap-pay/url?amount=0.01&subject=${encodeURIComponent('二维码测试支付')}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>二维码生成成功！</strong><br>
                            订单号: ${data.outTradeNo}<br>
                            支付链接: <a href="${data.paymentUrl}" target="_blank">点击支付</a><br><br>
                            <div style="text-align: center;">
                                <img src="${data.qrCodeUrl}" alt="支付二维码" style="border: 1px solid #ddd; border-radius: 4px;">
                                <br><small>使用支付宝扫码支付</small>
                            </div>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `生成失败: ${data.error}`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${error.message}`;
                });
        }

        // 测试API接口
        function testAPI() {
            const resultDiv = document.getElementById('quickResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试API...';

            const params = new URLSearchParams({
                outTradeNo: generateOrderNo(),
                totalAmount: '0.01',
                subject: 'API测试商品',
                description: '这是一个API接口测试'
            });

            fetch('/api/wap-pay/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `API测试成功！\n订单号: ${data.outTradeNo}\n消息: ${data.message}`;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `API测试失败: ${data.error}`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `API请求失败: ${error.message}`;
                });
        }

        // 处理自定义支付表单提交
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            // 如果订单号为空，则生成一个
            if (!formData.get('outTradeNo')) {
                formData.set('outTradeNo', generateOrderNo());
            }
            
            const resultDiv = document.getElementById('paymentResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在创建支付订单...';

            fetch('/api/wap-pay/create', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>支付订单创建成功！</strong><br>
                            订单号: ${data.outTradeNo}<br>
                            消息: ${data.message}<br><br>
                            <button onclick="showPaymentForm('${data.outTradeNo}')" class="quick-btn">查看支付表单</button>
                            <button onclick="openPayment('${data.outTradeNo}')" class="quick-btn">打开支付页面</button>
                        `;
                        // 保存支付表单以备后用
                        window.paymentForms = window.paymentForms || {};
                        window.paymentForms[data.outTradeNo] = data.paymentForm;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `创建失败: ${data.error}`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${error.message}`;
                });
        });

        // 处理二维码表单提交
        document.getElementById('qrForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = new URLSearchParams(formData);
            
            const resultDiv = document.getElementById('qrResult');
            const qrDiv = document.getElementById('qrCodeDisplay');
            
            resultDiv.style.display = 'block';
            qrDiv.style.display = 'none';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在生成二维码...';

            fetch(`/api/wap-pay/url?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>二维码生成成功！</strong><br>
                            订单号: ${data.outTradeNo}<br>
                            支付链接: <a href="${data.paymentUrl}" target="_blank">点击支付</a>
                        `;
                        
                        qrDiv.style.display = 'block';
                        qrDiv.innerHTML = `
                            <h3>扫码支付</h3>
                            <img src="${data.qrCodeUrl}" alt="支付二维码">
                            <p><small>请使用支付宝扫码支付</small></p>
                        `;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `生成失败: ${data.error}`;
                    }
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${error.message}`;
                });
        });

        // 显示支付表单HTML
        function showPaymentForm(orderNo) {
            if (window.paymentForms && window.paymentForms[orderNo]) {
                const newWindow = window.open('', '_blank');
                newWindow.document.write(window.paymentForms[orderNo]);
                newWindow.document.close();
            }
        }

        // 打开支付页面（通过临时表单提交）
        function openPayment(orderNo) {
            if (window.paymentForms && window.paymentForms[orderNo]) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = window.paymentForms[orderNo];
                const form = tempDiv.querySelector('form');
                if (form) {
                    form.target = '_blank';
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                }
            }
        }
    </script>
</body>
</html>