<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝 - 扫码支付</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        /* 支付宝头部样式 */
        .alipay-header {
            background: #108ee9;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        
        .alipay-logo {
            background: #108ee9;
            color: white;
            font-size: 18px;
            font-weight: bold;
            padding: 8px 12px;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
        
        /* 订单信息条 */
        .order-bar {
            background: #e8f4fd;
            padding: 12px 20px;
            border-bottom: 1px solid #d9d9d9;
            font-size: 14px;
            color: #666;
        }
        
        .order-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .amount-info {
            color: #ff6600;
            font-size: 18px;
            font-weight: bold;
        }
        
        /* 主要内容区域 */
        .main-content {
            background: white;
            padding: 40px;
            text-align: center;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .payment-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 30px;
            font-weight: normal;
        }
        
        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
        }
        
        #qrcode {
            margin-bottom: 20px;
        }
        
        .qr-instruction {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .download-links {
            color: #108ee9;
            font-size: 12px;
        }
        
        .download-links a {
            color: #108ee9;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .download-links a:hover {
            text-decoration: underline;
        }
        
        /* 支付状态样式 */
        .payment-status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
        }
        
        .payment-status.waiting {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .payment-status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .payment-status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        /* 底部信息 */
        .footer-info {
            text-align: center;
            padding: 20px;
            background: #f5f5f5;
            color: #999;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 支付宝头部 -->
    <div class="alipay-header">
        <div class="alipay-logo">
            支 支付宝
            <span style="font-size: 12px; margin-left: 8px;">ALIPAY</span>
        </div>
    </div>
    
    <!-- 订单信息条 -->
    <div class="order-bar">
        <div class="order-info">
            <div>
                正在使用即时到账交易 [?]
            </div>
        </div>
        <div style="margin-top: 8px;">
            <span th:text="${subject != null ? subject : '测试商品'}"></span>
            <span style="margin-left: 20px;">收款方：dqpoit9024@sandb...</span>
            <span class="amount-info" style="float: right;">
                <span th:text="${amount != null ? amount : '0.01'}"></span> 元
            </span>
        </div>
    </div>
    
    <!-- 主要支付内容 -->
    <div class="main-content">
        <div class="payment-title">扫码支付</div>
        
        <!-- 支付状态显示 -->
        <div class="payment-status waiting" id="paymentStatus">
            等待支付中...
        </div>
        
        <div class="qr-container">
            <!-- 二维码显示区域 -->
            <div id="qrcode"></div>
            
            <div class="qr-instruction">
                使用手机支付宝扫码完成付款
            </div>
            
            <div class="download-links">
                <a href="#">手机支付宝下载</a> | <a href="#">如何使用?</a>
            </div>
        </div>
    </div>
    
    <!-- 底部信息 -->
    <div class="footer-info">
        ICP证：合字B2-20190046
    </div>

    <script th:inline="javascript">
        // 获取二维码链接和订单号
        /*<![CDATA[*/
        var qrCodeUrl = /*[[${qrCode}]]*/ '';
        var outTradeNo = /*[[${outTradeNo}]]*/ '';
        console.log('QR Code URL length:', qrCodeUrl ? qrCodeUrl.length : 0);
        console.log('QR Code URL:', qrCodeUrl);
        console.log('Order No:', outTradeNo);
        /*]]>*/
        
        function generateQRCode() {
            if (qrCodeUrl) {
                console.log('Generating QR code for URL length:', qrCodeUrl.length);
                
                // 尝试使用其他二维码API
                tryGenerateQR();
                
                // 同时尝试加载Google Charts作为备用
                // const qrSize = 200;
                // const qrApiUrl = `https://chart.googleapis.com/chart?chs=${qrSize}x${qrSize}&cht=qr&chl=${encodeURIComponent(qrCodeUrl)}&choe=UTF-8`;
                // console.log('QR API URL:', qrApiUrl);
                
                // const qrImage = document.createElement('img');
                // qrImage.src = qrApiUrl;
                // qrImage.alt = '支付二维码';
                // qrImage.style.cssText = 'width: 200px; height: 200px; border: 1px solid #ddd;';
                
                // qrImage.onload = function() {
                //     // 二维码加载成功
                //     document.getElementById('qrcode').innerHTML = '';
                //     document.getElementById('qrcode').appendChild(qrImage);
                // };
                
                // qrImage.onerror = function() {
                //     console.log('QR image load failed');
                //     showFallback();
                // };
            } else {
                document.getElementById('qrcode').innerHTML = 
                    '<div style="color: red;">获取支付信息失败</div>';
            }
        }
        
        function tryGenerateQR() {
            // 尝试多个二维码服务
            const apis = [
                `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeUrl)}`,
                `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodeURIComponent(qrCodeUrl)}&choe=UTF-8`
            ];
            
            let currentApi = 0;
            
            function tryNextApi() {
                if (currentApi >= apis.length) {
                    showFallback();
                    return;
                }
                
                const img = document.createElement('img');
                img.src = apis[currentApi];
                img.alt = '支付二维码';
                img.style.cssText = 'width: 200px; height: 200px; border: 1px solid #ddd;';
                
                img.onload = function() {
                    document.getElementById('qrcode').innerHTML = '';
                    document.getElementById('qrcode').appendChild(img);
                    console.log('QR code loaded successfully from:', apis[currentApi]);
                };
                
                img.onerror = function() {
                    console.log('QR API failed:', apis[currentApi]);
                    currentApi++;
                    setTimeout(tryNextApi, 500);
                };
            }
            
            tryNextApi();
        }
        
        function showFallback() {
            // 备用方案：显示支付链接和手动跳转按钮
            if (qrCodeUrl) {
                document.getElementById('qrcode').innerHTML = `
                    <div style="padding: 20px; border: 1px solid #e6e6e6; border-radius: 4px;">
                        <p style="margin: 10px 0; color: #666; font-size: 14px;">二维码加载失败，请点击下方按钮跳转支付</p>
                        <button onclick="window.open('${qrCodeUrl}', '_blank')" 
                                style="background: #1890ff; color: white; padding: 10px 20px; 
                                       border: none; border-radius: 4px; font-size: 14px; cursor: pointer;">
                            打开支付页面
                        </button>
                    </div>
                `;
            } else {
                document.getElementById('qrcode').innerHTML = 
                    '<div style="color: #ff4d4f; font-size: 14px;">获取支付信息失败</div>';
            }
        }
        
        // 页面加载完成后生成二维码
        document.addEventListener('DOMContentLoaded', function() {
            generateQRCode();
            // 开始检查支付状态
            if (outTradeNo) {
                startPaymentStatusCheck();
            }
        });
        
        // 支付状态检查
        function startPaymentStatusCheck() {
            let checkCount = 0;
            const maxChecks = 60; // 最多检查5分钟
            
            function checkStatus() {
                if (checkCount >= maxChecks) {
                    updatePaymentStatus('支付超时，请重新发起支付', 'error');
                    return;
                }
                
                fetch(`/api/payments/alipay/status/${outTradeNo}`)
                    .then(response => response.json())
                    .then(data => {
                        console.log('Payment status:', data);
                        
                        if (data.status === 'SUCCESS') {
                            updatePaymentStatus('支付成功！', 'success');
                            // 可以在这里添加跳转逻辑
                            setTimeout(() => {
                                // window.location.href = '/payment/success?orderNo=' + outTradeNo;
                                alert('支付成功！订单号：' + outTradeNo);
                            }, 1000);
                        } else if (data.status === 'WAITING') {
                            checkCount++;
                            setTimeout(checkStatus, 3000); // 3秒后再次检查
                        } else {
                            updatePaymentStatus(data.message || '支付失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error checking payment status:', error);
                        checkCount++;
                        if (checkCount < maxChecks) {
                            setTimeout(checkStatus, 3000);
                        }
                    });
            }
            
            // 第一次检查延迟5秒
            setTimeout(checkStatus, 5000);
        }
        
        function updatePaymentStatus(message, type) {
            const statusElement = document.getElementById('paymentStatus');
            statusElement.className = 'payment-status ' + type;
            statusElement.textContent = message;
        }
    </script>
</body>
</html>