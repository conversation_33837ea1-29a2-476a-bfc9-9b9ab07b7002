<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择充值金额</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .payment-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            margin: 0 auto;
            min-height: auto;
        }

        .amount-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .amount-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: all 0.2s;
        }

        .amount-btn:hover {
            border-color: #007bff;
            color: #007bff;
        }

        .amount-btn.active {
            background: #e7f3ff;
            border-color: #007bff;
            color: #007bff;
        }

        .amount-btn.custom {
            color: #666;
        }

        .payment-methods {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .payment-method {
            flex: 1;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .payment-method:hover {
            border-color: #28a745;
        }

        .payment-method.active {
            border-color: #28a745;
            background: #f0f9f0;
        }

        .payment-method img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        .payment-method-name {
            font-size: 14px;
            color: #333;
        }

        .qr-section {
            text-align: center;
            padding: 15px 0;
            border-top: 1px solid #eee;
        }

        .qr-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .qr-amount {
            font-size: 32px;
            color: #ff4757;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }

        .qr-amount::before {
            content: '支付 ';
            font-size: 16px;
            color: #666;
            font-weight: normal;
        }

        .qr-amount::after {
            content: ' 元';
            font-size: 18px;
            color: #666;
            font-weight: normal;
        }

        .qr-code-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: white;
            border: 1px solid #e6e6e6;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 15px auto;
            width: 280px;
            height: 280px;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            display: block;
            margin: 0 auto;
        }

        .qr-iframe {
            width: 240px;
            height: 240px;
            border: none;
            border-radius: 4px;
            background: white;
            margin: 0;
            display: block;
        }

        #qrcode {
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            width: 100%;
        }

        #qrcode img {
            margin: 0 auto;
            display: block;
        }

        .qr-tip {
            margin-top: 20px;
            color: #999;
            font-size: 14px;
            line-height: 1.5;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .error {
            display: none;
            text-align: center;
            color: #ff4757;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <!-- 金额选择 -->
        <div class="amount-grid">
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn" data-amount="368.00">¥368.00</div>
            <div class="amount-btn custom" data-amount="custom">自定义</div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-methods">
            <div class="payment-method" data-method="wechat">
                <div style="width: 24px; height: 24px; background: #00d924; border-radius: 4px; margin-right: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;">微</div>
                <span class="payment-method-name">微信支付</span>
            </div>
            <div class="payment-method active" data-method="alipay">
                <div style="width: 24px; height: 24px; background: #1677ff; border-radius: 4px; margin-right: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px; font-weight: bold;">支</div>
                <span class="payment-method-name">支付宝支付</span>
            </div>
        </div>

        <!-- 二维码区域 -->
        <div class="qr-section">
            <div class="loading">正在生成支付二维码...</div>
            <div class="error">支付二维码生成失败</div>

            <div id="qr-content">
                <div class="qr-title">支付宝扫码</div>
                <div class="qr-amount" id="display-amount">368</div>
                <div class="qr-code-container">
                    <div id="qrcode"></div>
                </div>
                <div class="qr-tip">请使用支付宝扫码完成支付</div>
            </div>
        </div>
    </div>

    <script>
        let selectedAmount = 368.00;
        let selectedMethod = 'alipay';
        let currentOrderNo = null;

        // 金额选择
        document.querySelectorAll('.amount-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                if (this.dataset.amount === 'custom') {
                    const customAmount = prompt('请输入自定义金额:', '100.00');
                    if (customAmount && !isNaN(customAmount) && parseFloat(customAmount) > 0) {
                        selectedAmount = parseFloat(customAmount);
                        this.textContent = '¥' + selectedAmount.toFixed(2);
                    } else {
                        this.classList.remove('active');
                        return;
                    }
                } else {
                    selectedAmount = parseFloat(this.dataset.amount);
                }
                
                updateAmount();
                generatePayment();
            });
        });

        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('active'));
                this.classList.add('active');
                selectedMethod = this.dataset.method;
                generatePayment();
            });
        });

        // 更新显示金额
        function updateAmount() {
            document.getElementById('display-amount').textContent = selectedAmount;
        }

        // 生成支付
        function generatePayment() {
            if (selectedMethod !== 'alipay') {
                showError('暂时只支持支付宝支付');
                return;
            }

            showLoading();

            // 使用模式4生成iframe嵌入式支付
            fetch('/qr-pay-mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    'subject': '账户充值',
                    'amount': selectedAmount.toFixed(2),
                    'description': '用户账户充值 ' + selectedAmount.toFixed(2) + ' 元',
                    'mode': '4'  // 使用模式4：可定义宽度的嵌入式二维码
                })
            })
            .then(response => response.text())
            .then(html => {
                hideLoading();
                // 解析返回的HTML，提取支付表单
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // 查承付表单内容
                let paymentForm = null;
                
                // 尝试从Thymeleaf模板中获取paymentForm属性
                const iframeElement = doc.querySelector('iframe[srcdoc]');
                if (iframeElement) {
                    paymentForm = iframeElement.getAttribute('srcdoc');
                } else {
                    // 备用方案：直接查找form元素
                    const formElement = doc.querySelector('form');
                    if (formElement) {
                        paymentForm = formElement.outerHTML;
                    }
                }
                
                if (paymentForm) {
                    displayIframePayment(paymentForm);

                    // 尝试从支付表单中提取交易号
                    const tradeNoMatch = paymentForm.match(/out_trade_no['"]\s*[:\s]*['"](.*?)['"]/);
                    if (tradeNoMatch && tradeNoMatch[1]) {
                        const tradeNo = tradeNoMatch[1];
                        console.log('开始监控支付状态，交易号:', tradeNo);
                        // 延迟5秒后开始检查支付状态，给用户时间扫码
                        setTimeout(() => {
                            startPaymentStatusCheck(tradeNo);
                        }, 5000);
                    } else {
                        console.log('无法提取交易号，使用时间戳作为备用');
                        // 使用当前时间戳作为备用交易号
                        const backupTradeNo = 'PAY_' + Date.now();
                        setTimeout(() => {
                            startPaymentStatusCheck(backupTradeNo);
                        }, 5000);
                    }
                } else {
                    showError('获取支付表单失败');
                }
            })
            .catch(error => {
                hideLoading();
                showError('网络错误，请重试');
                console.error('Error:', error);
            });
        }

        // 显示二维码
        function displayQRCode(url) {
            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = '';

            // 使用在线二维码API生成
            const qrImg = document.createElement('img');
            qrImg.className = 'qr-code';
            qrImg.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
            qrImg.alt = '支付二维码';

            qrImg.onload = function() {
                qrContainer.appendChild(qrImg);
                showQRContent();
            };

            qrImg.onerror = function() {
                // 备用方案
                qrImg.src = `https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=${encodeURIComponent(url)}&choe=UTF-8`;
            };
        }
        
        // 显示iframe支付
        function displayIframePayment(formHtml) {
            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = '';

            // 在formHtml中添加居中样式
            const styledFormHtml = `
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    html, body {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: transparent;
                    }
                    body > * {
                        margin: 0 auto !important;
                        text-align: center !important;
                        display: block !important;
                    }
                    img {
                        margin: 0 auto !important;
                        display: block !important;
                    }
                </style>
                ${formHtml}
            `;

            // 创建iframe元素
            const iframe = document.createElement('iframe');
            iframe.className = 'qr-iframe';
            iframe.srcdoc = styledFormHtml;

            // 监听iframe加载完成，确保内容居中
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const body = iframeDoc.body;
                    if (body) {
                        // 强制设置body样式
                        body.style.cssText = `
                            margin: 0 !important;
                            padding: 0 !important;
                            display: flex !important;
                            justify-content: center !important;
                            align-items: center !important;
                            width: 100% !important;
                            height: 100% !important;
                            text-align: center !important;
                        `;

                        // 找到所有图片并居中
                        const images = body.querySelectorAll('img');
                        images.forEach(img => {
                            img.style.cssText = `
                                margin: 0 auto !important;
                                display: block !important;
                            `;
                        });
                    }
                } catch (e) {
                    // 跨域限制，无法直接操作
                    console.log('无法访问iframe内容，使用CSS样式');
                }
            };

            qrContainer.appendChild(iframe);
            showQRContent();
        }

        function showLoading() {
            document.querySelector('.loading').style.display = 'block';
            document.querySelector('.error').style.display = 'none';
            document.getElementById('qr-content').style.display = 'none';
        }

        function hideLoading() {
            document.querySelector('.loading').style.display = 'none';
        }

        function showError(message) {
            document.querySelector('.error').textContent = message;
            document.querySelector('.error').style.display = 'block';
            document.getElementById('qr-content').style.display = 'none';
        }

        function showQRContent() {
            document.querySelector('.error').style.display = 'none';
            document.getElementById('qr-content').style.display = 'block';
        }

        // 显示支付成功页面
        function showPaymentSuccess() {
            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = `
                <div style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 30px;
                    text-align: center;
                    background: white;
                    border-radius: 8px;
                    width: 240px;
                    height: 240px;
                    box-sizing: border-box;
                ">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: #52c41a;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 20px;
                    ">
                        <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3">
                            <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                    </div>
                    <div style="
                        font-size: 18px;
                        font-weight: bold;
                        color: #52c41a;
                        margin-bottom: 10px;
                    ">支付成功</div>
                    <div style="
                        font-size: 14px;
                        color: #666;
                        line-height: 1.5;
                    ">感谢您的支付<br>订单已完成</div>
                </div>
            `;
        }

        // 支付状态检查
        let paymentCheckInterval;
        let currentTradeNo = null;

        function startPaymentStatusCheck(tradeNo) {
            currentTradeNo = tradeNo;
            // 每3秒检查一次支付状态
            paymentCheckInterval = setInterval(() => {
                checkPaymentStatus(tradeNo);
            }, 3000);
        }

        function stopPaymentStatusCheck() {
            if (paymentCheckInterval) {
                clearInterval(paymentCheckInterval);
                paymentCheckInterval = null;
            }
        }

        function checkPaymentStatus(tradeNo) {
            fetch('/api/payment/status?tradeNo=' + encodeURIComponent(tradeNo))
                .then(response => response.json())
                .then(data => {
                    console.log('支付状态检查结果:', data);

                    if (data.status === 'SUCCESS' || data.status === 'TRADE_SUCCESS') {
                        stopPaymentStatusCheck();
                        showPaymentSuccess();

                        // 3秒后可以选择跳转或刷新页面
                        setTimeout(() => {
                            // 可以在这里添加跳转逻辑
                            // window.location.href = '/success';
                        }, 3000);
                    } else if (data.status === 'WAITING') {
                        // 继续等待，不需要额外操作，定时器会继续检查
                        console.log('支付等待中...');
                    } else {
                        console.log('支付状态:', data.status);
                    }
                })
                .catch(error => {
                    console.log('支付状态检查失败:', error);
                });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选择第一个金额
            document.querySelector('.amount-btn').classList.add('active');
            updateAmount();
            generatePayment();
        });
    </script>
</body>
</html>