<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付宝...</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-align: center;
            padding: 50px;
            background: #f5f5f5;
        }
        .loading {
            background: white;
            max-width: 500px;
            margin: 100px auto;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            width: 40px;
            height: 40px;
            margin: 20px auto;
            background-color: #1890ff;
            border-radius: 100%;
            animation: sk-scaleout 1.0s infinite ease-in-out;
        }
        @keyframes sk-scaleout {
            0% { 
                transform: scale(0);
            } 100% {
                transform: scale(1.0);
                opacity: 0;
            }
        }
        .message {
            color: #666;
            font-size: 16px;
            margin-top: 20px;
        }
        .mode-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="loading">
        <h2>🚀 正在跳转到支付宝扫码支付...</h2>
        <div class="spinner"></div>
        <div class="message">
            订单号: <strong th:text="${outTradeNo}"></strong><br>
            扫码模式: <strong th:text="${mode}"></strong><br>
            请稍等，正在为您跳转到支付页面...
        </div>
        
        <div class="mode-info">
            <strong>🔍 扫码模式说明：</strong><br>
            <span th:if="${mode == '0'}">模式0: 跳转模式（传统网页支付）</span>
            <span th:if="${mode == '1'}">模式1: 扫码模式（显示二维码供用户扫描）</span>
            <span th:if="${mode == '2'}">模式2: 混合模式（同时支持跳转和扫码）</span>
            <span th:if="${mode == '3'}">模式3: 订阅支付模式</span>
            <span th:if="${mode == '4'}">模式4: 嵌入式扫码模式（页面内嵌二维码）</span>
            <span th:if="${mode == '5'}">模式5: 新版扫码支付</span>
            <span th:if="${mode == '6'}">模式6: 设备扫码支付</span>
            <span th:if="${mode == '7'}">模式7: 测试模式</span>
        </div>
    </div>
    
    <!-- 支付宝支付表单，会自动提交 -->
    <div th:utext="${paymentForm}"></div>
    
    <script>
        // 5秒后如果还没跳转，显示提示
        setTimeout(function() {
            document.querySelector('.message').innerHTML = 
                '如果没有自动跳转，请检查是否被浏览器拦截了弹窗。<br>' +
                '<a href="javascript:history.back()">返回上一页</a>';
        }, 5000);
    </script>
</body>
</html>