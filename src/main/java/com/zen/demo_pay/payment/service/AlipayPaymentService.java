package com.zen.demo_pay.payment.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.zen.demo_pay.config.AlipayProperties;
import com.zen.demo_pay.payment.dto.AlipayPaymentRequest;
import com.zen.demo_pay.payment.dto.AlipayPaymentResponse;
import com.zen.demo_pay.payment.dto.AlipayQrCodeResponse;
import com.zen.demo_pay.payment.exception.PaymentException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Service
public class AlipayPaymentService {

    private static final String DEFAULT_PRODUCT_CODE = "FAST_INSTANT_TRADE_PAY";

    private final AlipayClient alipayClient;
    private final AlipayProperties properties;

    public AlipayPaymentService(AlipayClient alipayClient, AlipayProperties properties) {
        this.alipayClient = alipayClient;
        this.properties = properties;
    }
    public AlipayPaymentResponse createPagePayment(AlipayPaymentRequest request) {
        return createPagePayment(request, null);
    }
    
    /**
     * 创建电脑网站支付（支持指定支付模式）
     * @param request 支付请求
     * @param qrPayMode 二维码支付模式："0"表示跳转模式，"1"表示扫码模式，"2"表示跳转或扫码模式，"4"表示嵌入式扫码模式
     * @return 支付响应
     */
    public AlipayPaymentResponse createPagePayment(AlipayPaymentRequest request, String qrPayMode) {
        // 创建 API请求对象
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        
        // 设置回调地址
        if (StringUtils.hasText(properties.getReturnUrl())) {
            alipayRequest.setReturnUrl(properties.getReturnUrl());
        }
        if (StringUtils.hasText(properties.getNotifyUrl())) {
            alipayRequest.setNotifyUrl(properties.getNotifyUrl());
        }
        
        // 创建业务请求参数对象
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();
        model.setOutTradeNo(request.outTradeNo());
        model.setProductCode(Optional.ofNullable(request.productCode()).orElse(DEFAULT_PRODUCT_CODE));
        model.setTotalAmount(request.totalAmount().toPlainString());
        model.setSubject(request.subject());
        if (StringUtils.hasText(request.description())) {
            model.setBody(request.description());
        }
        
        // 设置二维码支付模式
        if (StringUtils.hasText(qrPayMode)) {
            model.setQrPayMode(qrPayMode);
        }
        
        // 设置更多自定义选项
        model.setTimeoutExpress("30m"); // 订单超时时间
        // model.setPassbackParams("custom_data"); // 公用回传参数
        // model.setGoodsType("0"); // 商品类型: 0-虚拟商品 1-实物商品
        // model.setStoreId("store_001"); // 商户门店编号
        
        alipayRequest.setBizModel(model);
        
        try {
            // 调用SDK生成表单
            String form = alipayClient.pageExecute(alipayRequest).getBody();
            return new AlipayPaymentResponse(form, request.outTradeNo());
        } catch (AlipayApiException e) {
            throw new PaymentException("Failed to create Alipay payment", e);
        }
    }
    
    /**
     * 创建扫码支付订单（使用统一收单线下交易预创建接口）
     * @param request 支付请求
     * @return 包含二维码链接的响应
     */
    public AlipayQrCodeResponse createQrCodePayment(AlipayPaymentRequest request) {
        // 使用统一收单线下交易预创建接口（alipay.trade.precreate）
        AlipayTradePrecreateRequest alipayRequest = new AlipayTradePrecreateRequest();
        
        // 设置异步通知地址
        if (StringUtils.hasText(properties.getNotifyUrl())) {
            alipayRequest.setNotifyUrl(properties.getNotifyUrl());
        }
        
        // 创建业务请求参数对象
        AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
        model.setOutTradeNo(request.outTradeNo());
        model.setTotalAmount(request.totalAmount().toPlainString());
        model.setSubject(request.subject());
        if (StringUtils.hasText(request.description())) {
            model.setBody(request.description());
        }
        // 设置超时时间（可选）
        model.setTimeoutExpress("30m");
        
        alipayRequest.setBizModel(model);
        
        try {
            // 调用SDK生成预订单
            AlipayTradePrecreateResponse response = alipayClient.execute(alipayRequest);
            System.out.println("Precreate response: " + response.getBody()); // 调试日志
            
            if (response.isSuccess()) {
                // 获取二维码链接
                String qrCode = response.getQrCode();
                System.out.println("QR Code URL: " + qrCode); // 调试日志
                
                return new AlipayQrCodeResponse(
                    qrCode, 
                    request.outTradeNo(),
                    "SUCCESS"
                );
            } else {
                System.out.println("Precreate failed: " + response.getSubMsg());
                throw new PaymentException("Failed to create QR code: " + response.getSubMsg());
            }
        } catch (AlipayApiException e) {
            throw new PaymentException("Failed to create QR code payment: " + e.getErrMsg(), e);
        }
    }
    
    /**
     * 从 HTML 表单中提取 action URL
     */
    private String extractActionUrl(String html) {
        try {
            // 提取 action URL
            String actionPattern = "action=\"([^\"]+)\"";
            java.util.regex.Pattern r = java.util.regex.Pattern.compile(actionPattern);
            java.util.regex.Matcher m = r.matcher(html);
            String actionUrl = null;
            if (m.find()) {
                actionUrl = m.group(1);
            }
            
            // 提取 biz_content 参数
            String bizContentPattern = "name=\"biz_content\" value=\"([^\"]+)\"";
            java.util.regex.Pattern bizR = java.util.regex.Pattern.compile(bizContentPattern);
            java.util.regex.Matcher bizM = bizR.matcher(html);
            String bizContent = null;
            if (bizM.find()) {
                bizContent = bizM.group(1);
                // HTML实体解码
                bizContent = bizContent.replace("&quot;", "\"").replace("&amp;", "&");
            }
            
            if (actionUrl != null && bizContent != null) {
                // 构造完整的GET请求URL
                String fullUrl = actionUrl + "&biz_content=" + java.net.URLEncoder.encode(bizContent, "UTF-8");
                System.out.println("Full GET URL: " + fullUrl);
                return fullUrl;
            }
            
            return actionUrl; // fallback
        } catch (Exception e) {
            System.out.println("Error extracting action URL: " + e.getMessage());
            return null;
        }
    }
}
