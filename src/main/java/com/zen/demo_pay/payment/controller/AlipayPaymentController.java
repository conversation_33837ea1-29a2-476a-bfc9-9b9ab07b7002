package com.zen.demo_pay.payment.controller;

import com.zen.demo_pay.payment.dto.AlipayPaymentRequest;
import com.zen.demo_pay.payment.dto.AlipayPaymentResponse;
import com.zen.demo_pay.payment.service.AlipayPaymentService;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(path = "/api/payments/alipay", produces = MediaType.APPLICATION_JSON_VALUE)
public class AlipayPaymentController {

    private static final Logger log = LoggerFactory.getLogger(AlipayPaymentController.class);

    private final AlipayPaymentService paymentService;
    
    // 简单的内存存储，生产环境应该使用数据库
    private final ConcurrentMap<String, String> paymentStatus = new ConcurrentHashMap<>();

    public AlipayPaymentController(AlipayPaymentService paymentService) {
        this.paymentService = paymentService;
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AlipayPaymentResponse> createPayment(@Valid @RequestBody AlipayPaymentRequest request) {
        AlipayPaymentResponse response = paymentService.createPagePayment(request);
        return ResponseEntity.ok(response);
    }

    @GetMapping(path = "/return", produces = MediaType.TEXT_HTML_VALUE)
    public String handleReturn(@RequestParam Map<String, String> params) {
        log.info("Alipay synchronous return params: {}", params);
        return "Payment processing, please check order status.";
    }

    @PostMapping(path = "/notify", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE, produces = MediaType.TEXT_PLAIN_VALUE)
    public String handleNotify(@RequestParam Map<String, String> params) {
        log.info("Alipay async notify params: {}", params);
        
        try {
            // TODO: 在实际生产中应该验证签名
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                // 支付成功
                paymentStatus.put(outTradeNo, "SUCCESS");
                log.info("Payment success for order: {}", outTradeNo);
            } else {
                log.warn("Payment status {} for order: {}", tradeStatus, outTradeNo);
                paymentStatus.put(outTradeNo, tradeStatus);
            }
            
            return "success";
        } catch (Exception e) {
            log.error("Error handling notify", e);
            return "fail";
        }
    }
    
    @GetMapping(path = "/status/{outTradeNo}")
    public ResponseEntity<Map<String, Object>> getPaymentStatus(@PathVariable String outTradeNo) {
        String status = paymentStatus.getOrDefault(outTradeNo, "WAITING");
        
        Map<String, Object> result = Map.of(
            "outTradeNo", outTradeNo,
            "status", status,
            "message", getStatusMessage(status)
        );
        
        return ResponseEntity.ok(result);
    }
    
    private String getStatusMessage(String status) {
        return switch (status) {
            case "SUCCESS" -> "支付成功";
            case "TRADE_SUCCESS" -> "交易成功";
            case "TRADE_FINISHED" -> "交易完成";
            case "WAITING" -> "等待支付";
            default -> "支付状态：" + status;
        };
    }
}
