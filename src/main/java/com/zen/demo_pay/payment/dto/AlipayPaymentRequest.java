package com.zen.demo_pay.payment.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

public record AlipayPaymentRequest(
        @NotBlank(message = "subject is required")
        String subject,
        @NotBlank(message = "outTradeNo is required")
        @JsonAlias({"out_trade_no"})
        String outTradeNo,
        @NotNull(message = "totalAmount is required")
        @DecimalMin(value = "0.01", message = "totalAmount must be at least 0.01")
        @JsonAlias({"total_amount"})
        BigDecimal totalAmount,
        @JsonAlias({"product_code"})
        String productCode,
        String description
) {
}
