package com.zen.demo_pay.controller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.zen.demo_pay.config.AlipayProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 原生手机网络支付控制器
 * 提供简单的WAP支付接口，适用于手机浏览器和APP内嵌H5支付
 */
@RestController
@RequestMapping("/api/wap-pay")
public class WapPaymentController {

    private static final Logger log = LoggerFactory.getLogger(WapPaymentController.class);

    private final AlipayClient alipayClient;
    private final AlipayProperties properties;

    public WapPaymentController(AlipayClient alipayClient, AlipayProperties properties) {
        this.alipayClient = alipayClient;
        this.properties = properties;
    }

    /**
     * 创建手机网络支付订单
     * 
     * 使用方式：
     * POST /api/wap-pay/create
     * {
     *   "outTradeNo": "ORDER_20241226_001",
     *   "totalAmount": "0.01",
     *   "subject": "测试商品",
     *   "description": "这是一个测试商品"
     * }
     * 
     * @param outTradeNo 商户订单号（必填）
     * @param totalAmount 订单金额（必填）
     * @param subject 订单标题（必填）
     * @param description 订单描述（可选）
     * @return 包含支付表单的响应
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createWapPayment(
            @RequestParam String outTradeNo,
            @RequestParam BigDecimal totalAmount,
            @RequestParam String subject,
            @RequestParam(required = false) String description) {

        try {
            log.info("Creating WAP payment - Order: {}, Amount: {}, Subject: {}", 
                    outTradeNo, totalAmount, subject);

            // 创建支付宝WAP支付请求
            AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
            
            // 设置回调URL
            alipayRequest.setReturnUrl(properties.getReturnUrl());
            alipayRequest.setNotifyUrl(properties.getNotifyUrl());

            // 创建业务参数模型
            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.setOutTradeNo(outTradeNo);
            model.setTotalAmount(totalAmount.toPlainString());
            model.setSubject(subject);
            model.setProductCode("QUICK_WAP_WAY"); // WAP支付产品码
            
            if (description != null && !description.trim().isEmpty()) {
                model.setBody(description);
            }

            alipayRequest.setBizModel(model);

            // 调用支付宝SDK生成支付表单
            String paymentForm = alipayClient.pageExecute(alipayRequest).getBody();
            
            log.info("WAP payment form generated successfully for order: {}", outTradeNo);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("outTradeNo", outTradeNo);
            response.put("paymentForm", paymentForm);
            response.put("message", "Payment form generated successfully");
            
            return ResponseEntity.ok(response);

        } catch (AlipayApiException e) {
            log.error("Failed to create WAP payment for order: {} - Error: {}", outTradeNo, e.getErrMsg(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("outTradeNo", outTradeNo);
            errorResponse.put("error", "Payment creation failed: " + e.getErrMsg());
            errorResponse.put("errorCode", e.getErrCode());
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 快速创建支付订单（GET方式，便于测试）
     * 
     * 使用方式：
     * GET /api/wap-pay/quick?amount=0.01&subject=测试商品
     * 
     * @param amount 支付金额
     * @param subject 商品标题
     * @return 直接返回支付表单HTML
     */
    @GetMapping(value = "/quick", produces = MediaType.TEXT_HTML_VALUE)
    public String createQuickWapPayment(
            @RequestParam BigDecimal amount,
            @RequestParam String subject) {

        // 生成唯一订单号
        String outTradeNo = "WAP_" + System.currentTimeMillis();
        
        try {
            log.info("Creating quick WAP payment - Order: {}, Amount: {}, Subject: {}", 
                    outTradeNo, amount, subject);

            AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
            alipayRequest.setReturnUrl(properties.getReturnUrl());
            alipayRequest.setNotifyUrl(properties.getNotifyUrl());

            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.setOutTradeNo(outTradeNo);
            model.setTotalAmount(amount.toPlainString());
            model.setSubject(subject);
            model.setProductCode("QUICK_WAP_WAY");

            alipayRequest.setBizModel(model);

            // 直接返回支付表单HTML
            return alipayClient.pageExecute(alipayRequest).getBody();

        } catch (AlipayApiException e) {
            log.error("Failed to create quick WAP payment - Error: {}", e.getErrMsg(), e);
            return "<html><body><h2>支付创建失败</h2><p>错误信息：" + e.getErrMsg() + "</p></body></html>";
        }
    }

    /**
     * 提取支付URL（用于生成二维码）
     * 
     * 使用方式：
     * GET /api/wap-pay/url?amount=0.01&subject=测试商品
     * 
     * @param amount 支付金额
     * @param subject 商品标题
     * @return 包含支付URL的JSON响应
     */
    @GetMapping("/url")
    public ResponseEntity<Map<String, Object>> getWapPaymentUrl(
            @RequestParam BigDecimal amount,
            @RequestParam String subject) {

        String outTradeNo = "WAP_URL_" + System.currentTimeMillis();
        
        try {
            log.info("Generating WAP payment URL - Order: {}, Amount: {}, Subject: {}", 
                    outTradeNo, amount, subject);

            AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();
            alipayRequest.setReturnUrl(properties.getReturnUrl());
            alipayRequest.setNotifyUrl(properties.getNotifyUrl());

            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.setOutTradeNo(outTradeNo);
            model.setTotalAmount(amount.toPlainString());
            model.setSubject(subject);
            model.setProductCode("QUICK_WAP_WAY");

            alipayRequest.setBizModel(model);

            String paymentForm = alipayClient.pageExecute(alipayRequest).getBody();
            
            // 提取支付URL
            String paymentUrl = extractPaymentUrl(paymentForm);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("outTradeNo", outTradeNo);
            response.put("paymentUrl", paymentUrl);
            response.put("qrCodeUrl", "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" + 
                        java.net.URLEncoder.encode(paymentUrl, "UTF-8"));
            response.put("message", "Payment URL generated successfully");
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to generate WAP payment URL - Error: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("outTradeNo", outTradeNo);
            errorResponse.put("error", "URL generation failed: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 从支付表单中提取完整的支付URL
     */
    private String extractPaymentUrl(String html) {
        try {
            // 提取 action URL
            String actionPattern = "action=\"([^\"]+)\"";
            java.util.regex.Pattern actionRegex = java.util.regex.Pattern.compile(actionPattern);
            java.util.regex.Matcher actionMatcher = actionRegex.matcher(html);
            
            if (!actionMatcher.find()) {
                throw new RuntimeException("Cannot extract action URL from payment form");
            }
            
            String actionUrl = actionMatcher.group(1);
            
            // 提取所有input参数
            String inputPattern = "<input[^>]*name=\"([^\"]+)\"[^>]*value=\"([^\"]+)\"[^>]*>";
            java.util.regex.Pattern inputRegex = java.util.regex.Pattern.compile(inputPattern);
            java.util.regex.Matcher inputMatcher = inputRegex.matcher(html);
            
            StringBuilder params = new StringBuilder();
            while (inputMatcher.find()) {
                String name = inputMatcher.group(1);
                String value = inputMatcher.group(2);
                
                // HTML实体解码
                value = value.replace("&quot;", "\"").replace("&amp;", "&").replace("&lt;", "<").replace("&gt;", ">");
                
                if (params.length() > 0) {
                    params.append("&");
                }
                params.append(name).append("=").append(java.net.URLEncoder.encode(value, "UTF-8"));
            }
            
            return actionUrl + "?" + params.toString();
            
        } catch (Exception e) {
            log.error("Error extracting payment URL: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 支付返回处理（同步回调）
     */
    @GetMapping("/return")
    public String handleReturn(@RequestParam Map<String, String> params) {
        log.info("WAP payment return callback: {}", params);
        
        String outTradeNo = params.get("out_trade_no");
        String tradeNo = params.get("trade_no");
        String totalAmount = params.get("total_amount");
        
        return String.format("""
                <html>
                <head>
                    <title>支付结果</title>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                </head>
                <body>
                    <div style="text-align: center; padding: 50px;">
                        <h2>支付处理中...</h2>
                        <p>商户订单号：%s</p>
                        <p>支付宝交易号：%s</p>
                        <p>支付金额：￥%s</p>
                        <p>请稍等，正在验证支付结果...</p>
                    </div>
                </body>
                </html>
                """, outTradeNo, tradeNo, totalAmount);
    }

    /**
     * 支付通知处理（异步回调）
     */
    @PostMapping(value = "/notify", produces = MediaType.TEXT_PLAIN_VALUE)
    public String handleNotify(@RequestParam Map<String, String> params) {
        log.info("WAP payment async notify: {}", params);
        
        // TODO: 在这里添加签名验证和业务逻辑处理
        // 验证通过后返回"success"，否则支付宝会继续重复通知
        
        return "success";
    }
}