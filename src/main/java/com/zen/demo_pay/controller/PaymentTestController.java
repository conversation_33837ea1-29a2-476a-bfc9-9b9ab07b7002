package com.zen.demo_pay.controller;

import com.zen.demo_pay.payment.dto.AlipayPaymentRequest;
import com.zen.demo_pay.payment.dto.AlipayPaymentResponse;
import com.zen.demo_pay.payment.dto.AlipayQrCodeResponse;
import com.zen.demo_pay.payment.service.AlipayPaymentService;
import com.zen.demo_pay.payment.controller.AlipayPaymentController;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Controller
public class PaymentTestController {

    private final AlipayPaymentService paymentService;
    private final AlipayPaymentController alipayPaymentController;

    public PaymentTestController(AlipayPaymentService paymentService, AlipayPaymentController alipayPaymentController) {
        this.paymentService = paymentService;
        this.alipayPaymentController = alipayPaymentController;
    }

    /**
     * 显示测试支付页面
     */
    @GetMapping("/")
    public String index() {
        return "payment-test";
    }
    
    /**
     * 显示自定义充值页面
     */
    @GetMapping("/custom-payment")
    public String customPayment() {
        return "custom-payment";
    }

    /**
     * 处理支付请求并返回支付表单页面
     */
    @PostMapping(value = "/pay", produces = MediaType.TEXT_HTML_VALUE)
    public String createPayment(@RequestParam String subject,
                              @RequestParam BigDecimal amount,
                              @RequestParam(required = false) String description) {
        
        // 生成唯一订单号
        String outTradeNo = "ORDER_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 创建支付请求
        AlipayPaymentRequest request = new AlipayPaymentRequest(
                subject, 
                outTradeNo, 
                amount, 
                null, 
                description
        );
        
        // 获取支付表单
        AlipayPaymentResponse response = paymentService.createPagePayment(request);
        
        // 直接返回HTML表单，浏览器会自动提交到支付宝
        return "redirect-form";
    }

    /**
     * 动态生成支付表单页面
     */
    @PostMapping(value = "/pay-form", produces = MediaType.TEXT_HTML_VALUE)
    public String getPaymentForm(@RequestParam String subject,
                                @RequestParam BigDecimal amount,
                                @RequestParam(required = false) String description,
                                Model model) {
        
        // 生成唯一订单号
        String outTradeNo = "ORDER_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 创建支付请求
        AlipayPaymentRequest request = new AlipayPaymentRequest(
                subject, 
                outTradeNo, 
                amount, 
                null, 
                description
        );
        
        // 获取支付表单
        AlipayPaymentResponse response = paymentService.createPagePayment(request);
        
        // 将表单HTML传递给模板
        model.addAttribute("paymentForm", response.form());
        model.addAttribute("outTradeNo", response.outTradeNo());
        
        return "payment-form";
    }
    
    /**
     * 生成二维码支付页面
     */
    @PostMapping(value = "/qr-pay")
    public String createQrPayment(@RequestParam String subject,
                                 @RequestParam BigDecimal amount,
                                 @RequestParam(required = false) String description,
                                 Model model) {
        
        // 生成唯一订单号
        String outTradeNo = "QR_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 创建支付请求
        AlipayPaymentRequest request = new AlipayPaymentRequest(
                subject, 
                outTradeNo, 
                amount, 
                null, 
                description
        );
        
        // 获取二维码
        AlipayQrCodeResponse response = paymentService.createQrCodePayment(request);
        
        // 传递数据到模板
        model.addAttribute("qrCode", response.qrCode());
        model.addAttribute("outTradeNo", response.outTradeNo());
        model.addAttribute("subject", subject);
        model.addAttribute("amount", amount);
        
        return "qr-payment";
    }
    
    /**
     * 使用电脑网站支付接口生成二维码（从HTML表单中提取支付URL）
     */
    @PostMapping(value = "/qr-pay-page")
    public String createQrPaymentFromPage(@RequestParam String subject,
                                         @RequestParam BigDecimal amount,
                                         @RequestParam(required = false) String description,
                                         Model model) {
        
        // 生成唯一订单号
        String outTradeNo = "PAGE_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 创建支付请求
        AlipayPaymentRequest request = new AlipayPaymentRequest(
                subject, 
                outTradeNo, 
                amount, 
                null, 
                description
        );
        
        // 获取支付表单
        AlipayPaymentResponse response = paymentService.createPagePayment(request);
        
        // 从HTML表单中提取action URL作为二维码内容
        String qrCode = extractActionUrlFromForm(response.form());
        
        // 传递数据到模板
        model.addAttribute("qrCode", qrCode);
        model.addAttribute("outTradeNo", response.outTradeNo());
        model.addAttribute("subject", subject);
        model.addAttribute("amount", amount);
        
        return "qr-payment";
    }
    
    /**
     * 从HTML表单中提取支付URL
     */
    private String extractActionUrlFromForm(String htmlForm) {
        try {
            // 提取action URL
            java.util.regex.Pattern actionPattern = java.util.regex.Pattern.compile("action=\"([^\"]+)\"");
            java.util.regex.Matcher actionMatcher = actionPattern.matcher(htmlForm);
            
            if (actionMatcher.find()) {
                return actionMatcher.group(1);
            }
        } catch (Exception e) {
            System.out.println("Error extracting action URL: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 使用电脑网站支付的扫码模式
     */
    @PostMapping(value = "/qr-pay-mode")
    public String createQrPaymentWithMode(@RequestParam String subject,
                                         @RequestParam BigDecimal amount,
                                         @RequestParam(required = false) String description,
                                         @RequestParam(defaultValue = "1") String mode,
                                         Model model) {
        
        // 生成唯一订单号
        String outTradeNo = "QRM_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // 创建支付请求
        AlipayPaymentRequest request = new AlipayPaymentRequest(
                subject, 
                outTradeNo, 
                amount, 
                null, 
                description
        );
        
        // 使用指定的扫码模式创建支付
        AlipayPaymentResponse response = paymentService.createPagePayment(request, mode);
        
        // 将表单HTML传递给模板
        model.addAttribute("paymentForm", response.form());
        model.addAttribute("outTradeNo", response.outTradeNo());
        model.addAttribute("mode", mode);
        model.addAttribute("subject", subject);
        model.addAttribute("amount", amount);
        
        // 前置模式(0,1,3,4)使用iframe，跳转模式(2)直接跳转
        if ("2".equals(mode)) {
            return "payment-form-qr"; // 跳转模式
        } else {
            return "iframe-payment"; // 前置模式，使用iframe
        }
    }

    /**
     * 通用支付状态查询API
     * 用于前端轮询检查支付状态
     */
    @GetMapping("/api/payment/status")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getPaymentStatus(@RequestParam String tradeNo) {
        // 委托给AlipayPaymentController处理
        return alipayPaymentController.getPaymentStatus(tradeNo);
    }
}
