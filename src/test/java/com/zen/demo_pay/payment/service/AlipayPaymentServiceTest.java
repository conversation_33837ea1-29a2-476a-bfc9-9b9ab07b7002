package com.zen.demo_pay.payment.service;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.zen.demo_pay.config.AlipayProperties;
import com.zen.demo_pay.payment.dto.AlipayPaymentRequest;
import com.zen.demo_pay.payment.dto.AlipayPaymentResponse;
import com.zen.demo_pay.payment.exception.PaymentException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AlipayPaymentServiceTest {

    @Mock
    private AlipayClient alipayClient;

    private AlipayPaymentService service;

    @BeforeEach
    void setUp() {
        AlipayProperties properties = new AlipayProperties();
        properties.setGateway("https://openapi-sandbox.dl.alipaydev.com/gateway.do");
        properties.setAppId("appId");
        properties.setMerchantPrivateKey("merchantKey");
        properties.setAlipayPublicKey("alipayKey");
        properties.setReturnUrl("http://localhost:8080/api/payments/alipay/return");
        properties.setNotifyUrl("http://localhost:8080/api/payments/alipay/notify");
        service = new AlipayPaymentService(alipayClient, properties);
    }

    @Test
    void createPagePaymentReturnsFormWhenSuccess() throws Exception {
        AlipayTradePagePayResponse response = new AlipayTradePagePayResponse();
        response.setBody("<form id=\"alipaysubmit\"></form>");
        response.setCode("10000");
        when(alipayClient.pageExecute(any(AlipayTradePagePayRequest.class))).thenReturn(response);

        AlipayPaymentRequest request = new AlipayPaymentRequest(
                "Test subject",
                "ORDER123",
                new BigDecimal("88.88"),
                null,
                null
        );

        AlipayPaymentResponse paymentResponse = service.createPagePayment(request);

        assertThat(paymentResponse.outTradeNo()).isEqualTo("ORDER123");
        assertThat(paymentResponse.form()).contains("alipaysubmit");

        ArgumentCaptor<AlipayTradePagePayRequest> captor = ArgumentCaptor.forClass(AlipayTradePagePayRequest.class);
        verify(alipayClient).pageExecute(captor.capture());
        // 验证请求参数设置正确
        assertThat(captor.getValue().getReturnUrl()).isEqualTo("http://localhost:8080/api/payments/alipay/return");
        assertThat(captor.getValue().getNotifyUrl()).isEqualTo("http://localhost:8080/api/payments/alipay/notify");
    }


    @Test
    void createPagePaymentThrowsWhenApiExceptionRaised() throws Exception {
        when(alipayClient.pageExecute(any(AlipayTradePagePayRequest.class)))
                .thenThrow(new AlipayApiException("invalid-signature"));

        AlipayPaymentRequest request = new AlipayPaymentRequest(
                "Test subject",
                "ORDER456",
                new BigDecimal("99.99"),
                null,
                null
        );

        assertThatThrownBy(() -> service.createPagePayment(request))
                .isInstanceOf(PaymentException.class)
                .hasMessageContaining("Failed to create Alipay payment");
    }
}
