# Repository Guidelines

## Project Structure & Module Organization
- `src/main/java/com/zen/demo_pay` holds the Spring Boot entry point; group new features into feature-focused subpackages such as `payment/controller`, `payment/service`, and `payment/model` to keep responsibilities isolated.
- Configuration lives in `src/main/resources/application.properties`; create profile-specific overrides (`application-dev.properties`, `application-prod.properties`) when you need environment tweaks.
- Mirror the source tree under `src/test/java` for unit and integration tests so packages line up and Spring can locate slices consistently.

## Build, Test, and Development Commands
- `mvn spring-boot:run` launches the application with classpath reloading, ideal for local development.
- `mvn test` runs the Spring Boot test suite (JUnit 5, Mockito, AssertJ) and should pass before every push.
- `mvn clean package` produces a runnable JAR in `target/`; use it to verify release readiness before tagging or deploying.

## Coding Style & Naming Conventions
- Target Java 21 with four-space indentation, UTF-8 source files, and trailing newline at EOF; disable tabs in your IDE.
- Keep package names lowercase; use PascalCase for classes (`PaymentController`), camelCase for methods/fields, and suffix components by role (`*Controller`, `*Service`, `*Repository`).
- Prefer constructor injection, Lombok-free code, and Spring annotations (`@RestController`, `@Service`) for discoverability.

## Testing Guidelines
- Co-locate tests with the production package they cover and name classes `*Tests` (e.g., `PaymentServiceTests`).
- Use JUnit 5 with Spring’s testing annotations (`@SpringBootTest`, `@WebMvcTest`) and Mockito for collaborators; keep unit tests fast and deterministic.
- Aim for meaningful coverage on business rules; integration tests should exercise HTTP endpoints or persistence boundaries.

## Commit & Pull Request Guidelines
- Write imperative, present-tense commit subjects under 50 characters (e.g., `Add payment capture flow`); include an optional body explaining the why.
- Group related changes per commit, reference issue IDs where applicable, and avoid committing secrets or generated artifacts.
- Pull requests should describe the change, highlight risk areas, list manual/verifiable tests, and attach screenshots or sample payloads for API tweaks.

## Configuration & Security Notes
- Never commit real credentials; load secrets via environment variables or a profile-specific `application-*.properties` ignored by Git.
- Document any new configuration keys in `application.properties` comments and call out required env vars in the PR description so deployers can mirror them.
