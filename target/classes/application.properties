spring.application.name=demo_pay

# ================ 支付宝沙箱配置 ================
# 注意：生产环境请使用正式网关，并且绝不要将密钥提交到版本控制系统

# 支付宝网关地址
# 沙箱环境: https://openapi-sandbox.dl.alipaydev.com/gateway.do
# 正式环境: https://openapi.alipay.com/gateway.do
alipay.gateway=https://openapi-sandbox.dl.alipaydev.com/gateway.do

# 应用ID (APPID)
# 获取方式: 登录支付宝开放平台 -> 进入应用详情 -> 查看APPID
# 示例: 2021000000000000
alipay.app-id=2021000148688731

# 商户私钥 (应用私钥)
# 获取方式: 使用支付宝提供的密钥生成工具生成RSA2密钥对，这里填私钥
# 格式: 去掉-----BEGIN PRIVATE KEY-----和-----END PRIVATE KEY-----的纯字符串
# 注意: 此密钥绝对不能泄露，建议生产环境使用环境变量
alipay.merchant-private-key=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCCFg9ZfhVmx5GNBMg1H4mxWG1h6Z1YavJRdl5h7+c8XlzwEwYS7RHKEX95tQKT+CPhWwdbD6IwyzQr0zK8zTrgw5BMjSVt9Emc4Pmqd59LsqhGJZOPfFe61kl4DySnQbJ260MZKCw7AGBx1wOuEj5aqoyWwvUXB8eGSn7XTam8DKchOMfKX9igV/YQpSCjN5x4zG+yFU09iTFdo+ImAMFIvB2AnuIug6MyfBJnuD5hpujbGwOun8K1LO30rzX25EwsU7pVaLNHSvuywKjVhA05uFIisb3frEEDMUvey3lb4jrbVEZNVKFGc4fWyfLTYwm4QdlhUjdEe7kjU6Iy4BCtAgMBAAECggEAJII+cpZhOUQ/RD2YVthwE3bG1Bj1kPAK6/P1jzT+R8IQAUzFgqBIQtbgWO8FNzQIFeIvUU407c7J6JbQgY5zvMsF/4YJgC90TXQSfdYuvdb5RR6cqEN0d7pWSjytw8c7aAalATV2HGUBrFF09fCm0VlWNaWnlXwYckcMDC7+uQjIpp+eoogoJW+Fufll8S3H4F8ydXmbhXoWZdnD/QflZfhJlwNEDc3Uw5PyEw6U6AfnRgoKlxyc4V+wT9Cb91Bk8zZTF/coSuyEMKq7+j9xFJV8Ze27sPMywJyXntCL/llempKh4bpKJXhiOtUUPx+941EGHcJjo3ZTB8QnIWuhfQKBgQDRPWX+/4khxqJOwNBbFVkBUDuVcqMSMR7vExCS1mM9D7OMauqXdR/rBjL1eyUvdwpbnBmF1BACEeq1X7Ys+9w6cdK5fWdbg3UFLXzmoQrfGy+y8g5sWTUZxn5Jbyl78Dnhcy06Y4LYdOPwTep4JjeGZxrwSHSnpDFlMNufCG2U3wKBgQCfKEiu2LC8T+USMqD0c3FG2luPwu6Mn2l+xHEvLSe0qRSZKsrk88rfzoWUPvnNz9XV2+ACXj13pnAkWgB8BEg+N45uI/RBiUi0REfwZnLCXPalhF5tj6rAsH66oC134CKYPCrTdJ/qsv0luK2b07zjzFEMmxejAKXDuVv7dgNf8wKBgQCoLBMIatYLIZ1ecraEHppU5emd6m+KtZRJTydo/YrxjucemuhGJ0sqGOdoSjvO9HyMuOACojjjkQb2FCFV8nBFQSq/Dz29XeD18mOK8jDMtQsXZlmVV/XH67OgzgmnpaK/w1S8szJ9SyXRBK7JNt411jTenz2zZ9KTpDtAjQOWPQKBgGsB67RbDqDd2rccgSk6ANxRbaQ/vKGhgI1xyGOeqYaG88lzgwziePF26krc+JmO9keeWINfF1a9+cIHHTJEssnaEpIs7Emv4skI65FPYDMby91HHTHumTCKufoCPe/OK5RNR8H6NJbZFujovRj7CJHkSk3+bKr0gX8Rvi1YkDZPAoGBAJ7Jy+RMYFbNcO/SBkRNvu9hhaKlJ+eEPflw6+tuQG5EuSnKNGPgmEMAzj7JyyKOB57ynWryL626v9apAoy6POSFi1EUkSslOVpP3K+wnprC99M9TN50UQ+b0OAHbxpoaihgT7z35pkpBpDlvMHZgrLqwc8vRW+4ezMMiyxk6I/z

# 支付宝公钥 (用于验证支付宝返回数据的签名)
# 获取方式: 支付宝开放平台 -> 应用详情 -> 开发设置 -> 接口加签方式 -> 查看支付宝公钥
# 格式: 去掉-----BEGIN PUBLIC KEY-----和-----END PUBLIC KEY-----的纯字符串
alipay.alipay-public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkmWXsEkVlXkoYBGRO3AyDFXtjG/LXr53T7BtdsL2zxVrY0bFKjhkZ9SzU48QPDhLe1SuymyMcCZOhsfT3J5aWzjb3dxYx2fY+hgeWt+O84CR+kw89lJxUF652/csyqS34gftpNaaT9Elmon6VAGu1Q+tSjKmX5fEx6XZs7i4PhoEHl3X3Iu2vITa8VlnMKYB/kyH8HsihU05lDHUfT09oIEk+QXIltOttdtNf0qCGtwprtyCBmemN4swpbe+lNzoB1/cmvYDP1GMwWQ5DUuAi7X2pIHifLocPN37J/OXV3eMJBPkbFlpDtgcpkvJZ42tUhZi44j5yyXnfQk11sNzwQIDAQAB

# 签名算法类型 (推荐使用RSA2)
# RSA2: 使用SHA256WithRSA算法，安全性更高
# RSA: 使用SHA1WithRSA算法 (不推荐)
alipay.sign-type=RSA2

# 同步回调地址 (支付完成后页面跳转地址)
# 用户支付成功后，支付宝会跳转到这个地址，并带上支付结果参数
# 注意: 必须是http://或https://开头的完整路径
alipay.return-url=http://localhost:8080/api/payments/alipay/return

# 异步通知地址 (服务器接收支付结果通知)
# 支付宝服务器会向这个地址发送POST请求，通知支付结果
# 注意: 必须是外网可以访问的地址，本地调试可以使用ngrok等内网穿透工具
# 当前使用ngrok生成的公网地址
alipay.notify-url=https://nonauthoritative-unripely-mara.ngrok-free.dev/api/payments/alipay/notify
