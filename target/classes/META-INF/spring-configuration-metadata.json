{"groups": [{"name": "alipay", "type": "com.zen.demo_pay.config.AlipayProperties", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}], "properties": [{"name": "alipay.alipay-public-key", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.api-version", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties", "defaultValue": "1.0"}, {"name": "alipay.app-id", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.charset", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.format", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties", "defaultValue": "JSON"}, {"name": "alipay.gateway", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.merchant-private-key", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.notify-url", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.return-url", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties"}, {"name": "alipay.sign-type", "type": "java.lang.String", "sourceType": "com.zen.demo_pay.config.AlipayProperties", "defaultValue": "RSA2"}], "hints": [], "ignored": {"properties": []}}