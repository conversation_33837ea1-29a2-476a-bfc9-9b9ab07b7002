<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付宝...</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            text-align: center;
            padding: 50px;
            background: #f5f5f5;
        }
        .loading {
            background: white;
            max-width: 500px;
            margin: 100px auto;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            width: 40px;
            height: 40px;
            margin: 20px auto;
            background-color: #1890ff;
            border-radius: 100%;
            animation: sk-scaleout 1.0s infinite ease-in-out;
        }
        @keyframes sk-scaleout {
            0% { 
                transform: scale(0);
            } 100% {
                transform: scale(1.0);
                opacity: 0;
            }
        }
        .message {
            color: #666;
            font-size: 16px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="loading">
        <h2>🚀 正在跳转到支付宝...</h2>
        <div class="spinner"></div>
        <div class="message">
            订单号: <strong th:text="${outTradeNo}"></strong><br>
            请稍等，正在为您跳转到支付页面...
        </div>
    </div>
    
    <!-- 支付宝支付表单，会自动提交 -->
    <div th:utext="${paymentForm}"></div>
    
    <script>
        // 5秒后如果还没跳转，显示提示
        setTimeout(function() {
            document.querySelector('.message').innerHTML = 
                '如果没有自动跳转，请检查是否被浏览器拦截了弹窗。<br>' +
                '<a href="javascript:history.back()">返回上一页</a>';
        }, 5000);
    </script>
</body>
</html>