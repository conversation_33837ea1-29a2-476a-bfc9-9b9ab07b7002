<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝 - 扫码支付</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        .payment-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .payment-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .iframe-container {
            text-align: center;
            background: white;
            border-radius: 6px;
            padding: 20px;
        }
        
        .payment-iframe {
            border: 1px solid #e6e6e6;
            border-radius: 6px;
        }
        
        .mode-info {
            margin-top: 20px;
            padding: 15px;
            background: #e6f7ff;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h1 class="payment-title">💳 支付宝扫码支付</h1>
        
        <div class="order-info">
            <div><strong>订单号：</strong><span th:text="${outTradeNo}"></span></div>
            <div><strong>商品：</strong><span th:text="${subject}"></span></div>
            <div><strong>金额：</strong><span th:text="${amount}">0.01</span> 元</div>
            <div><strong>支付模式：</strong><span th:text="${mode}"></span></div>
        </div>
        
        <div class="iframe-container">
            <iframe class="payment-iframe" 
                    th:srcdoc="${paymentForm}"
                    th:width="${mode == '0' ? '600' : (mode == '1' ? '300' : (mode == '3' ? '75' : '400'))}"
                    th:height="${mode == '0' ? '300' : (mode == '1' ? '600' : (mode == '3' ? '75' : '300'))}"
                    frameborder="0">
            </iframe>
        </div>
        
        <div class="mode-info">
            <strong>🔍 当前支付模式说明：</strong><br>
            <span th:if="${mode == '0'}">模式0: 简约前置模式 (600px × 300px)</span>
            <span th:if="${mode == '1'}">模式1: 前置模式 (300px × 600px)</span>
            <span th:if="${mode == '3'}">模式3: 迷你前置模式 (75px × 75px)</span>
            <span th:if="${mode == '4'}">模式4: 可定义宽度的嵌入式二维码</span>
            <span th:if="${mode == '2'}">模式2: 跳转模式（不适用于iframe）</span>
        </div>
    </div>
</body>
</html>