<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝支付测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 5px rgba(24,144,255,0.3);
        }
        .pay-button {
            background: #1890ff;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        .pay-button:hover {
            background: #096dd9;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .example {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💳 支付宝支付测试</h1>
        
        <div class="info">
            <strong>📋 使用说明:</strong><br>
            1. 填写商品信息和金额<br>
            2. 点击"扫码支付"按钮<br>
            3. 使用支付宝APP扫描二维码完成支付
        </div>

        <form action="/qr-pay" method="post">
            <div class="form-group">
                <label for="subject">商品名称 *</label>
                <input type="text" id="subject" name="subject" value="测试商品" required>
                <div class="example">例如: iPhone 15 Pro Max</div>
            </div>

            <div class="form-group">
                <label for="amount">支付金额 (元) *</label>
                <input type="number" id="amount" name="amount" value="0.01" min="0.01" step="0.01" required>
                <div class="example">测试建议使用 0.01 元</div>
            </div>

            <div class="form-group">
                <label for="description">商品描述</label>
                <textarea id="description" name="description" rows="3" placeholder="商品的详细描述信息...">这是一个支付测试商品</textarea>
            </div>

            <div style="display: flex; flex-direction: column; gap: 10px;">
                <button type="submit" class="pay-button" style="background: #52c41a;" formaction="/qr-pay">
                    📱 扫码支付（alipay.trade.precreate）
                </button>
                
                <div style="margin: 10px 0; font-size: 14px; color: #666; text-align: center;">
                    🔍 电脑网站支付嵌入模式测试：
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; max-width: 600px; margin: 0 auto;">
                    <button type="submit" class="pay-button" style="background: #1890ff;" formaction="/qr-pay-mode" onclick="this.form.elements['mode'].value='0'">
                        📋 模式0 - 简约前置模式
                    </button>
                    <button type="submit" class="pay-button" style="background: #722ed1;" formaction="/qr-pay-mode" onclick="this.form.elements['mode'].value='1'">
                        📱 模式1 - 前置模式
                    </button>
                    <button type="submit" class="pay-button" style="background: #fa8c16;" formaction="/qr-pay-mode" onclick="this.form.elements['mode'].value='2'">
                        🔄 模式2 - 跳转模式
                    </button>
                    <button type="submit" class="pay-button" style="background: #f5222d;" formaction="/qr-pay-mode" onclick="this.form.elements['mode'].value='3'">
                        🔍 模式3 - 迷你前置模式
                    </button>
                    <button type="submit" class="pay-button" style="background: #13c2c2;" formaction="/qr-pay-mode" onclick="this.form.elements['mode'].value='4'">
                        💻 模式4 - 嵌入式二维码
                    </button>
                </div>
                
                <input type="hidden" name="mode" value="1">
            </div>
            
            <div style="margin-top: 15px; padding: 10px; background: #fff7e6; border-radius: 5px; font-size: 14px;">
                📝 <strong>注意：</strong>绿色按钮需要申请“当面付”签约，其他按钮使用已有的“电脑网站支付”签约。
            </div>
            
            <div style="margin-top: 20px; text-align: center;">
                <a href="/custom-payment" target="_blank" style="display: inline-block; background: #52c41a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                    🎆 查看自定义充值页面效果
                </a>
            </div>
        </form>

        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>🔒 这是测试环境，使用支付宝沙箱账号进行支付</p>
            <p><strong>沙箱买家账号:</strong> <EMAIL></p>
            <p><strong>登录密码:</strong> 111111</p>
            <p><strong>支付密码:</strong> 111111</p>
        </div>
    </div>
</body>
</html>