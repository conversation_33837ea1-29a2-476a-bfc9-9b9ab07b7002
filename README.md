# 支付宝支付Demo

这是一个集成支付宝SDK的Spring Boot支付演示项目。

## 功能特性

- 集成官方支付宝SDK (alipay-sdk-java 4.39.218.ALL)
- 支持支付宝电脑网站支付 (alipay.trade.page.pay)
- 支持同步回调和异步通知
- 完整的配置管理
- 单元测试覆盖

## 项目结构

```
src/
├── main/java/com/zen/demo_pay/
│   ├── config/
│   │   ├── AlipayConfig.java          # Alipay客户端配置
│   │   └── AlipayProperties.java      # 支付宝配置属性
│   ├── payment/
│   │   ├── controller/
│   │   │   ├── AlipayPaymentController.java    # 支付控制器
│   │   │   └── PaymentExceptionHandler.java    # 异常处理器
│   │   ├── dto/
│   │   │   ├── AlipayPaymentRequest.java       # 支付请求DTO
│   │   │   └── AlipayPaymentResponse.java      # 支付响应DTO
│   │   ├── exception/
│   │   │   └── PaymentException.java           # 支付异常
│   │   └── service/
│   │       └── AlipayPaymentService.java       # 支付服务
│   └── DemoPayApplication.java
└── test/java/com/zen/demo_pay/
    └── payment/service/
        └── AlipayPaymentServiceTest.java        # 服务层测试
```

## 配置说明

### 📋 快速配置

在 `application.properties` 中配置支付宝参数：

```properties
# 支付宝沙箱配置 (生产环境请使用正式网关)
alipay.gateway=https://openapi-sandbox.dl.alipaydev.com/gateway.do
alipay.app-id=YOUR_APP_ID
alipay.merchant-private-key=YOUR_MERCHANT_PRIVATE_KEY
alipay.alipay-public-key=ALIPAY_DEV_PUBLIC_KEY
alipay.sign-type=RSA2
alipay.return-url=http://localhost:8080/api/payments/alipay/return
alipay.notify-url=http://localhost:8080/api/payments/alipay/notify
```

### 📖 详细配置指南

如果您不知道如何获取上述配置参数，请查看详细的配置指南：

**[👉 点击查看支付宝配置参数获取指南](./ALIPAY_CONFIG_GUIDE.md)**

该指南详细说明了：
- 🔑 如何生成和配置 RSA2 密钥对
- 🏢 如何在支付宝开放平台获取 APPID
- 🌐 如何获取支付宝公钥
- 🔧 本地开发环境的配置方法
- ⚠️ 常见问题解决方案

**重要提示：**
- 请使用支付宝开放平台分配的应用ID
- 商户私钥需要妖善保管，不能泄露
- 生产环境请使用正式网关地址
- 回调URL需要公网可访问

## API使用示例

### 1. 创建支付订单

**请求：**
```http
POST /api/payments/alipay
Content-Type: application/json

{
    "subject": "商品标题",
    "outTradeNo": "ORDER20251226001",
    "totalAmount": 99.99,
    "description": "商品描述信息"
}
```

**响应：**
```json
{
    "form": "<form action=\"https://openapi-sandbox.dl.alipaydev.com/gateway.do\">...</form>",
    "outTradeNo": "ORDER20251226001"
}
```

返回的`form`字段包含自动提交的HTML表单，可以直接在浏览器中显示进行支付。

### 2. 支付回调处理

**同步回调 (GET):**
```
GET /api/payments/alipay/return?out_trade_no=ORDER20251226001&trade_no=...
```

**异步通知 (POST):**
```
POST /api/payments/alipay/notify
Content-Type: application/x-www-form-urlencoded

out_trade_no=ORDER20251226001&trade_status=TRADE_SUCCESS&...
```

## 运行项目

1. 确保已安装Java 21和Maven
2. 配置支付宝参数到`application.properties`
3. 运行应用：

```bash
mvn spring-boot:run
```

应用启动后访问：http://localhost:8080

## 测试

运行单元测试：

```bash
mvn test
```

## 依赖说明

主要依赖：
- Spring Boot 3.5.6
- Alipay SDK Java 4.39.218.ALL
- Spring Boot Validation

## 安全注意事项

1. **私钥管理**: 商户私钥绝不能泄露，建议使用环境变量或配置中心管理
2. **签名验证**: 生产环境必须验证支付宝的异步通知签名
3. **HTTPS**: 生产环境必须使用HTTPS
4. **回调验证**: 异步通知需要验证订单状态和金额等关键信息

## 生产环境部署

1. 将网关地址修改为正式环境：`https://openapi.alipay.com/gateway.do`
2. 配置真实的应用ID和密钥
3. 设置公网可访问的回调URL
4. 添加签名验证逻辑
5. 完善异常处理和日志记录

---

更多详细信息请参考 [支付宝开发文档](https://opendocs.alipay.com/)