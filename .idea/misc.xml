<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="KubernetesApiProvider"><![CDATA[{}]]></component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id>Gradle</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Kotlin，迁移</id>
          </State>
          <State>
            <id>Maven</id>
          </State>
          <State>
            <id>MavenKotlin，迁移</id>
          </State>
          <State>
            <id>可能的 bugGradle</id>
          </State>
          <State>
            <id>安全性</id>
          </State>
        </expanded-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="21" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>