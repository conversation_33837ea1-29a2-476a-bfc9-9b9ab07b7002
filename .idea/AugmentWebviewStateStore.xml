<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;7d28b314-a0f9-4e13-8acf-456142752ab8&quot;,&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;conversations&quot;:{&quot;7d28b314-a0f9-4e13-8acf-456142752ab8&quot;:{&quot;id&quot;:&quot;7d28b314-a0f9-4e13-8acf-456142752ab8&quot;,&quot;name&quot;:&quot;&quot;,&quot;createdAtIso&quot;:&quot;2025-09-26T12:23:49.520Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-09-26T12:24:37.241Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;617c8bcb-bb13-43c3-a76f-0e6685e7e8d4&quot;,&quot;uuid&quot;:&quot;e5301dc0-810e-428e-ae1b-3d4981f4ea8a&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1758889429521,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;094a5bfd-3d46-43c4-9a99-d4fbb9efb060&quot;,&quot;timestamp&quot;:&quot;2025-09-26T12:24:03.212Z&quot;,&quot;request_message&quot;:&quot;你好&quot;,&quot;status&quot;:&quot;failed&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;temp-fe-48cacc62-4ed0-4fce-8a2e-fc5e780e22a4&quot;,&quot;timestamp&quot;:&quot;2025-09-26T12:24:37.242Z&quot;,&quot;request_message&quot;:&quot;阅读我的项目，然后给我接入alpay 使用沙箱测试&quot;,&quot;status&quot;:&quot;cancelled&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;7409b71d-59f5-417b-940b-625940d7a76b&quot;,&quot;status&quot;:&quot;cancelled&quot;,&quot;chatItemType&quot;:&quot;agentic-turn-delimiter&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-7995112a-d6a3-49d4-9972-b34762729bad&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-48cacc62-4ed0-4fce-8a2e-fc5e780e22a4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false,&quot;hasTitleGenerated&quot;:true},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;f7fcec9c-d158-4631-8725-6c1a2467d8cb&quot;}}}" />
      </map>
    </option>
  </component>
</project>